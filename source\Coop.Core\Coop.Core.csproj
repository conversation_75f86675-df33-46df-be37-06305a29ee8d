﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{442B73F2-B103-4EF6-8B4B-A2F36F169515}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Coop.Core</RootNamespace>
    <AssemblyName>Coop.Core</AssemblyName>
    <TargetFramework>netstandard2.0</TargetFramework>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <LangVersion>10</LangVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Platforms>x64</Platforms>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;</DefineConstants>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>10</LangVersion>
    <StartAction>Program</StartAction>
    <PlatformTarget>x64</PlatformTarget>
    <StartWorkingDirectory>$(SolutionDir)..\mb2\bin\Win64_Shipping_Client</StartWorkingDirectory>
    <StartProgram>$(SolutionDir)..\mb2\bin\Win64_Shipping_Client\Bannerlord.exe</StartProgram>
    <StartArguments>/singleplayer /server _MODULES_*Native*SandBoxCore*CustomBattle*SandBox*StoryMode*Coop*_MODULES_</StartArguments>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>false</Optimize>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>10</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Client\Services\MobileParties\Messages\Data\**" />
    <Compile Remove="Server\Services\Armies\Messages\**" />
    <Compile Remove="Server\Services\Kingdoms\Messages\**" />
    <EmbeddedResource Remove="Client\Services\MobileParties\Messages\Data\**" />
    <EmbeddedResource Remove="Server\Services\Armies\Messages\**" />
    <EmbeddedResource Remove="Server\Services\Kingdoms\Messages\**" />
    <None Remove="Client\Services\MobileParties\Messages\Data\**" />
    <None Remove="Server\Services\Armies\Messages\**" />
    <None Remove="Server\Services\Kingdoms\Messages\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Autofac" Version="8.2.0" />
    <PackageReference Include="Krafs.Publicizer" Version="2.3.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Collections.Immutable" Version="9.0.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{e474a5b5-3f73-46cb-b68c-e8fa5199950b}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\GameInterface\GameInterface.csproj">
      <Project>{D22CB8FA-40CC-4F47-986F-1C5A4415980B}</Project>
      <Name>GameInterface</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="FodyWeavers.xml" />
  </ItemGroup>
  <ItemGroup>
	<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
		<_Parameter1>Coop.Tests</_Parameter1>
	</AssemblyAttribute>
	<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
		<_Parameter1>Coop.IntegrationTests</_Parameter1>
	</AssemblyAttribute>
	<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
		<_Parameter1>E2E.Tests</_Parameter1>
	</AssemblyAttribute>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Server\Services\Kingdoms\Messages\NetworkAddDecision.cs" />
    <Compile Include="Server\Services\Kingdoms\Messages\NetworkRemoveDecision.cs" />
  </ItemGroup>
  <ItemGroup>
	  <Reference Include="TaleWorlds.Library">
		  <HintPath>..\..\mb2\bin\Win64_Shipping_Client\TaleWorlds.Library.dll</HintPath>
	  </Reference>
  </ItemGroup>
  <ItemGroup>
    <Publicize Include="TaleWorlds.Library" />
  </ItemGroup>
</Project>