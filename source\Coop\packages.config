﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Lib.Harmony" version="2.3.5" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.2" targetFramework="net472" />
  <package id="Mono.Cecil" version="0.11.6" targetFramework="net472" />
  <package id="MonoMod.Backports" version="1.1.2" targetFramework="net472" />
  <package id="MonoMod.Core" version="1.2.3" targetFramework="net472" />
  <package id="MonoMod.ILHelpers" version="1.1.0" targetFramework="net472" />
  <package id="MonoMod.RuntimeDetour" version="25.2.3" targetFramework="net472" />
  <package id="MonoMod.Utils" version="25.0.8" targetFramework="net472" />
  <package id="Serilog" version="4.2.0" targetFramework="net472" />
  <package id="Serilog.Enrichers.Process" version="3.0.0" targetFramework="net472" />
  <package id="Serilog.Sinks.Debug" version="3.0.0" targetFramework="net472" />
  <package id="Serilog.Sinks.File" version="6.0.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.6.0" targetFramework="net472" />
  <package id="System.Collections.Immutable" version="9.0.2" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.2" targetFramework="net472" />
  <package id="System.IO.Pipelines" version="9.0.2" targetFramework="net472" />
  <package id="System.Memory" version="4.6.0" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.6.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="9.0.2" targetFramework="net472" />
  <package id="System.Text.Json" version="9.0.2" targetFramework="net472" />
  <package id="System.Threading.Channels" version="9.0.2" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.0" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
</packages>