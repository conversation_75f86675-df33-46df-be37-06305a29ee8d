---
name: Issue Template
about: Default issue template
title: "[ISSUE]"
labels: ''
assignees: ''

---

# For New Contributors
[How to contribute](https://github.com/Bannerlord-Coop-Team/BannerlordCoop/wiki/Getting-Started-as-a-Contributor)

## Description
<!-- Describe why this issue is needed. -->


## Intended Design
<!-- Provide any relevant design documents, create any if complexity requires. -->


## Location
<!-- Add where changes for this issue will exist. -->
<!-- Add any related files here. -->
Create a branch based from [development](https://github.com/Bannerlord-Coop-Team/BannerlordCoop)

## Related Issues
<!-- Add any related issues here (Child of #EPIC/ Blocked by #SOMETHING). -->


## Requirements
<!-- Add testable requirements if needed. -->


## Additional information
<!-- Add all information that might be useful while working on this issue here. (e.g. places in the code to look at) -->


## Definition of Done
- [ ] Class level comments exist for all new classes.
- [ ] XUnit tests exist for every method that does not require the game to be ran.
<!-- Create more required items as needed. -->
