<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1751px" height="731px" viewBox="-0.5 -0.5 1751 731" content="&lt;mxfile host=&quot;2f23d0d8-37a5-4a73-8668-caf1a6ada8e5&quot; modified=&quot;2020-09-26T11:17:39.340Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.49.1 Chrome/83.0.4103.122 Electron/9.2.1 Safari/537.36&quot; etag=&quot;aI4p9Vl7mLO6aVZWF0PP&quot; version=&quot;13.1.3&quot;&gt;&lt;diagram id=&quot;poV7IlEdQqrj2gPoaiXM&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="1270" y="0" width="480" height="730" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 478px; height: 1px; padding-top: 7px; margin-left: 1272px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: left; ">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                Client side (ALL client!): execut method call
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1272" y="22" fill="#000000" font-family="Helvetica" font-size="15px" font-weight="bold">
                    Client side (ALL client!): execut method call
                </text>
            </switch>
        </g>
        <rect x="820" y="0" width="440" height="730" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 438px; height: 1px; padding-top: 7px; margin-left: 822px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: left; ">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                Server side: broadcast arguments &amp; method call
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="822" y="22" fill="#000000" font-family="Helvetica" font-size="15px" font-weight="bold">
                    Server side: broadcast arguments &amp; method call
                </text>
            </switch>
        </g>
        <rect x="840" y="250" width="360" height="90" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 295px; margin-left: 1020px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                Railgun
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1020" y="299" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Railgun
                </text>
            </switch>
        </g>
        <path d="M 1177 257 L 1193 257 L 1193 277 L 1177 277 L 1177 273 L 1173 273 L 1173 269 L 1177 269 L 1177 265 L 1173 265 L 1173 261 L 1177 261 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1177 261 L 1181 261 L 1181 265 L 1177 265 M 1177 269 L 1181 269 L 1181 273 L 1177 273" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="0" y="0" width="810" height="730" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 808px; height: 1px; padding-top: 7px; margin-left: 2px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: left; ">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                Client side: queue synchronized method call
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2" y="22" fill="#000000" font-family="Helvetica" font-size="15px" font-weight="bold">
                    Client side: queue synchronized method call
                </text>
            </switch>
        </g>
        <path d="M 340 295 L 180 295 L 180 196.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 180 191.12 L 183.5 198.12 L 180 196.37 L 176.5 198.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 251px; margin-left: 181px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;registersGlobalHandler&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="181" y="254" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;registersGlobalHandler&gt;
                </text>
            </switch>
        </g>
        <path d="M 500 295 L 613.63 295" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 618.88 295 L 611.88 298.5 L 613.63 295 L 611.88 291.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 296px; margin-left: 560px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;raises&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="560" y="299" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;raises&gt;
                </text>
            </switch>
        </g>
        <path d="M 420 270 L 420 196.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 420 191.12 L 423.5 198.12 L 420 196.37 L 416.5 198.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 227px; margin-left: 421px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;creates&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="421" y="230" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;creates&gt;
                </text>
            </switch>
        </g>
        <path d="M 420 320 L 420 393.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 420 398.88 L 416.5 391.88 L 420 393.63 L 423.5 391.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 355px; margin-left: 418px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;uses&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="418" y="358" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;uses&gt;
                </text>
            </switch>
        </g>
        <rect x="340" y="270" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 295px; margin-left: 420px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                MethodCallSyncHandler
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="299" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    MethodCallSyncHandler
                </text>
            </switch>
        </g>
        <rect x="100" y="140" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 165px; margin-left: 180px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                MethodAccess
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="169" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    MethodAccess
                </text>
            </switch>
        </g>
        <path d="M 780 295 L 833.63 295" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 838.88 295 L 831.88 298.5 L 833.63 295 L 831.88 291.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="620" y="270" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 295px; margin-left: 700px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                EventMethodCall
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="299" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EventMethodCall
                </text>
            </switch>
        </g>
        <path d="M 500 165 L 700 165 L 700 263.63" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 700 268.88 L 696.5 261.88 L 700 263.63 L 703.5 261.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 166px; margin-left: 637px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;storedIn&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="637" y="169" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;storedIn&gt;
                </text>
            </switch>
        </g>
        <rect x="340" y="140" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 165px; margin-left: 420px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                MethodCall
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="169" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    MethodCall
                </text>
            </switch>
        </g>
        <path d="M 420 450 L 420 518.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 420 523.88 L 416.5 516.88 L 420 518.63 L 423.5 516.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 483px; margin-left: 421px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;uses&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="421" y="486" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;uses&gt;
                </text>
            </switch>
        </g>
        <path d="M 340 425 L 266.37 425" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 261.12 425 L 268.12 421.5 L 266.37 425 L 268.12 428.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="340" y="400" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 425px; margin-left: 420px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                ArgumentFactory
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="429" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ArgumentFactory
                </text>
            </switch>
        </g>
        <path d="M 500 550 L 675 550 L 675 605 L 843.63 605" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 848.88 605 L 841.88 608.5 L 843.63 605 L 841.88 601.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 551px; margin-left: 619px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;sync&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="619" y="554" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;sync&gt;
                </text>
            </switch>
        </g>
        <rect x="340" y="525" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 550px; margin-left: 420px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                RemoteStore
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="554" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    RemoteStore
                </text>
            </switch>
        </g>
        <path d="M 180 450 L 180 518.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 180 523.88 L 176.5 516.88 L 180 518.63 L 183.5 516.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 478px; margin-left: 179px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;creates&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="179" y="481" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;creates&gt;
                </text>
            </switch>
        </g>
        <rect x="100" y="400" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 425px; margin-left: 180px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                ArgumentSerializer
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="429" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ArgumentSerializer
                </text>
            </switch>
        </g>
        <path d="M 260 550 L 333.63 550" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 338.88 550 L 331.88 553.5 L 333.63 550 L 331.88 546.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="100" y="525" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 550px; margin-left: 180px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                Argument
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="554" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Argument
                </text>
            </switch>
        </g>
        <path d="M 307.5 600 L 300 550" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 260 600 L 358 600 L 370 612 L 370 700 L 260 700 L 260 600 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 358 600 L 358 612 L 370 612" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 650px; margin-left: 261px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Depending on the argument, the factory may put it into the store and transfer just the store id.
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="315" y="654" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Depending on the a...
                </text>
            </switch>
        </g>
        <path d="M 465 120 L 445 140" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 460 20 L 558 20 L 570 32 L 570 120 L 460 120 L 460 20 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 558 20 L 558 32 L 570 32" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 70px; margin-left: 461px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                A MethodCall consist of a statically assigned MethodId and the serialized arguments as a list
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="515" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    A MethodCall consi...
                </text>
            </switch>
        </g>
        <path d="M 930 340 L 930 383.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 930 388.88 L 926.5 381.88 L 930 383.63 L 933.5 381.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1200 295 L 1313.63 295" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1318.88 295 L 1311.88 298.5 L 1313.63 295 L 1311.88 291.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 296px; margin-left: 1263px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;broadcast&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1263" y="299" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;broadcast&gt;
                </text>
            </switch>
        </g>
        <path d="M 1110 340 L 1110 295 L 1193.63 295" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 1198.88 295 L 1191.88 298.5 L 1193.63 295 L 1191.88 291.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 930 440 L 930 462.5 L 1050 462.5 L 1050 478.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1050 483.88 L 1046.5 476.88 L 1050 478.63 L 1053.5 476.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 463px; margin-left: 977px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;addsTo&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="977" y="467" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;addsTo&gt;
                </text>
            </switch>
        </g>
        <rect x="850" y="390" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 415px; margin-left: 930px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                EventMethodCall
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="930" y="419" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EventMethodCall
                </text>
            </switch>
        </g>
        <path d="M 1050 535 L 1050 557.5 L 930 557.5 L 930 573.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 930 578.88 L 926.5 571.88 L 930 573.63 L 933.5 571.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 558px; margin-left: 1000px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;waitsFor&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="562" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;waitsFor&gt;
                </text>
            </switch>
        </g>
        <path d="M 1130 510 L 1110 510 L 1110 346.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1110 341.12 L 1113.5 348.12 L 1110 346.37 L 1106.5 348.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 409px; margin-left: 1110px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;broadcastEvent&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1110" y="412" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;broadcastEvent&gt;
                </text>
            </switch>
        </g>
        <rect x="970" y="485" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 510px; margin-left: 1050px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                EventBroadcastingQueue
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1050" y="514" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EventBroadcastingQueue
                </text>
            </switch>
        </g>
        <path d="M 1010 605 L 1630 605 L 1630 446.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1630 441.12 L 1633.5 448.12 L 1630 446.37 L 1626.5 448.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 607px; margin-left: 1344px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;sync&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1344" y="610" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;sync&gt;
                </text>
            </switch>
        </g>
        <rect x="850" y="580" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 605px; margin-left: 930px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                SharedRemoteStore
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="930" y="609" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SharedRemoteStore
                </text>
            </switch>
        </g>
        <path d="M 840 295 L 930 295 L 930 333.63" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 930 338.88 L 926.5 331.88 L 930 333.63 L 933.5 331.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1480 295 L 1543.63 295" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1548.88 295 L 1541.88 298.5 L 1543.63 295 L 1541.88 291.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 296px; margin-left: 1511px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;uses&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1511" y="299" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;uses&gt;
                </text>
            </switch>
        </g>
        <path d="M 1400 270 L 1400 196.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1400 191.12 L 1403.5 198.12 L 1400 196.37 L 1396.5 198.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 238px; margin-left: 1401px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;callOriginal&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1401" y="241" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;callOriginal&gt;
                </text>
            </switch>
        </g>
        <rect x="1320" y="270" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 295px; margin-left: 1400px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                EventMethodCall
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1400" y="299" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EventMethodCall
                </text>
            </switch>
        </g>
        <path d="M 540 367 L 461.67 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 540 350 L 638 350 L 650 362 L 650 450 L 540 450 L 540 350 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 638 350 L 638 362 L 650 362" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 400px; margin-left: 541px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                The method call is not executed! It is intercepted and sent to the server
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="595" y="404" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    The method call is...
                </text>
            </switch>
        </g>
        <path d="M 1180 498.7 L 1130 503.04" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1180 430 L 1368 430 L 1380 442 L 1380 550 L 1180 550 L 1180 430 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1368 430 L 1368 442 L 1380 442" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 490px; margin-left: 1181px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                The broadcasting queue waits for all arguments to be distributed to all clients through the shared store. Once every client is ready, the event is broadcast through Railgun
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1280" y="494" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    The broadcasting queue waits for...
                </text>
            </switch>
        </g>
        <path d="M 1260.71 220 L 1353.57 270" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1110 160 L 1288 160 L 1300 172 L 1300 220 L 1110 220 L 1110 160 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1288 160 L 1288 172 L 1300 172" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 188px; height: 1px; padding-top: 190px; margin-left: 1111px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                The event is sent to all clients, including the one that invoked it!
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1205" y="194" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    The event is sent to all client...
                </text>
            </switch>
        </g>
        <path d="M 1630 270 L 1630 196.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1630 191.12 L 1633.5 198.12 L 1630 196.37 L 1626.5 198.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 237px; margin-left: 1632px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;resolves&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1632" y="240" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;resolves&gt;
                </text>
            </switch>
        </g>
        <path d="M 1630 320 L 1630 383.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1630 388.88 L 1626.5 381.88 L 1630 383.63 L 1633.5 381.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 360px; margin-left: 1631px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;uses&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1631" y="364" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;uses&gt;
                </text>
            </switch>
        </g>
        <rect x="1550" y="270" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 295px; margin-left: 1630px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                ArgumentFactory
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1630" y="299" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ArgumentFactory
                </text>
            </switch>
        </g>
        <path d="M 1550 165 L 1486.37 165" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 1481.12 165 L 1488.12 161.5 L 1486.37 165 L 1488.12 168.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="1550" y="140" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 165px; margin-left: 1630px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                Argument
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1630" y="169" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Argument
                </text>
            </switch>
        </g>
        <rect x="1320" y="140" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 165px; margin-left: 1400px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                MethodCall
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1400" y="169" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    MethodCall
                </text>
            </switch>
        </g>
        <rect x="1550" y="390" width="160" height="50" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 415px; margin-left: 1630px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                RemoteStore
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1630" y="419" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    RemoteStore
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://desk.draw.io/support/solutions/articles/16000042487" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>